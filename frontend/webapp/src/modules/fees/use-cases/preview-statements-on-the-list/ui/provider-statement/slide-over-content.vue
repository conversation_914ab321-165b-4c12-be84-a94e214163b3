<template>
  <box class="h-full shadow-none">
    <box-section
      divider="bottom"
      class="-my-6 text-start"
      title="Provider statement"
    />
    <box-section no-padding-x class="h-full"
      ><provider-statement-form
        :can-save="viewModel.canSave()"
        :can-cancel="viewModel.canCancel()"
        :can-add-another="viewModel.canAddAnother()"
        :initial-values="viewModel.getFormValues()"
        :validation-schema="viewModel.getValidationSchema()"
        :ctx-data="viewModel.getContextData()"
        @on-save-and-close="
          (e) => {
            viewModel.setContextData({ statementLines: e.statementLines });
            viewModel
              .onSave(e.formValues)
              .then(() => $emit('on-save-and-close'))
              .catch((e) => $emit('on-save-error', e));
          }
        "
        @on-cancel-and-close="(e) => $emit('on-cancel-and-close', e)"
    /></box-section>
  </box>
</template>

<script setup lang="ts">
  import { Box, BoxSection } from '@modules/ui';
  import ProviderStatementForm from '@modules/fees/ui/forms/statement/provider-statement-form.vue';
  import { ProviderViewModel as ViewModel } from '@modules/fees/ui/forms/statement/view-model';

  defineProps<{ viewModel: ViewModel }>();

  defineEmits([
    'on-save-and-close',
    'on-save-error',
    'on-save-and-add-another',
    'on-cancel-and-close',
  ]);
</script>
