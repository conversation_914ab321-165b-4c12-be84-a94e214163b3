import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { Amplify } from 'aws-amplify';
import AmplifyVue from '@aws-amplify/ui-vue';
import { fetchAuthSession } from 'aws-amplify/auth';
import { vMaska } from 'maska/vue';
import * as ConfirmDialog from 'vuejs-confirm-dialog';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import Toast, { POSITION, PluginOptions } from 'vue-toastification';

import { API_NAME } from '@aventur-shared/services/api';
import { useAPIState } from '@aventur-shared/composables/useAPIState';
import { toastPluginOptions } from '@aventur-shared/composables/useToast';
import { yupValidationMethods } from '@aventur-shared/plugins';

import sentry from '@/plugins/sentry';
import { default as amplitude } from '@/plugins/amplitude';

import App from './App.vue';
import router from './router';

import './assets/css/style.pcss';

const toastOptions: PluginOptions = {
  ...toastPluginOptions,
  position: POSITION.TOP_CENTER,
  hideProgressBar: true,
  closeButton: false,
  timeout: 2500,
  containerClassName: ['iphone-notch-safe'],
  transition: 'Vue-Toastification__fade',
};

import awsConfig from './aws-exports';
//

Amplify.configure(awsConfig, {
  API: {
    REST: {
      headers: async (options: { apiName: string }) => {
        if (options.apiName !== API_NAME) {
          return {} as Record<string, string>;
        }

        const { idToken } = (await fetchAuthSession()).tokens ?? {};
        return {
          Authorization: `Bearer ${idToken}`,
        };
      },
      retryStrategy: {
        strategy: 'no-retry',
      },
    },
  },
});

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

router.beforeEach((/* to, from */) => {
  const { setError: setAPIError } = useAPIState();
  setAPIError(undefined); // clear API error if any
});

const app = createApp(App);

// directives
app.directive('maska', vMaska);

app.use(sentry);
app.use(amplitude);
app.use(router);
app.use(AmplifyVue);
app.use(pinia);
app.use(ConfirmDialog);
app.use(Toast, toastOptions);
app.use(yupValidationMethods);

app.mount('#app');
