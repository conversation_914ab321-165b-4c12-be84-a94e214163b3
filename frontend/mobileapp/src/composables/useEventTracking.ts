import { RouteParamsRawGeneric, Router } from 'vue-router';

import { setAmplitudeUserDetails, track } from '@/plugins/amplitude';
//

const AMPLITUDE_EVENT_TYPE = 'Aventur';

export enum AventurEvents {
  ClientSignedIn = 'client-signed-in',
  ClientSignedOut = 'client-signed-out',
  ClientSignedUp = 'client-signed-up',
  ClientViewedPage = 'client-viewed-page',
  ClientSubmittedFeedback = 'client-submitted-feedback',
  ClientClearedChat = 'client-cleared-chat',
  ClientCreatedGoal = 'client-created-goal',
  ClientUpdatedGoal = 'client-updated-goal',
  ClientDeletedGoal = 'client-deleted-goal',
  ClientLinkedGoalAccounts = 'client-linked-goal-accounts',
  ClientUnlinkedGoalAccounts = 'client-unlinked-goal-accounts',
  ClientCreatedAsset = 'client-created-asset',
  ClientUpdatedAsset = 'client-updated-asset',
  ClientDeletedAsset = 'client-deleted-asset',
  ClientCreatedDebt = 'client-created-debt',
  ClientUpdatedDebt = 'client-updated-debt',
  ClientDeletedDebt = 'client-deleted-debt',
  ClientCreatedIncome = 'client-created-income',
  ClientUpdatedIncome = 'client-updated-income',
  ClientDeletedIncome = 'client-deleted-income',
  ClientCreatedExpenditure = 'client-created-expenditure',
  ClientUpdatedExpenditure = 'client-updated-expenditure',
  ClientDeletedExpenditure = 'client-deleted-expenditure',
  ClientUpdatedProfile = 'client-updated-profile',
  ClientUpdatedAccountSettings = 'client-updated-account-settings',
}

interface IAventurEvent<T extends AventurEvents = AventurEvents> {
  event: T;
  display_name: string;
  options?: Record<string, unknown>;
}

export interface ClientSignedInEvent
  extends IAventurEvent<AventurEvents.ClientSignedIn> {
  event: AventurEvents.ClientSignedIn;
}

export interface ClientSignedOutEvent
  extends IAventurEvent<AventurEvents.ClientSignedOut> {
  event: AventurEvents.ClientSignedOut;
}

export interface ClientSignedUpEvent
  extends IAventurEvent<AventurEvents.ClientSignedUp> {
  event: AventurEvents.ClientSignedUp;
}

export interface ClientClearedChatEvent
  extends IAventurEvent<AventurEvents.ClientClearedChat> {
  event: AventurEvents.ClientClearedChat;
}

export interface ClientSubmittedFeedbackEvent
  extends IAventurEvent<AventurEvents.ClientSubmittedFeedback> {
  event: AventurEvents.ClientSubmittedFeedback;
  options: {
    current_page: string;
  };
}

export interface ClientViewedPageEvent
  extends IAventurEvent<AventurEvents.ClientViewedPage> {
  event: AventurEvents.ClientViewedPage;
  options: {
    page_name: string | undefined;
    params: RouteParamsRawGeneric;
  };
}

export interface ClientCreatedGoalEvent
  extends IAventurEvent<AventurEvents.ClientCreatedGoal> {
  event: AventurEvents.ClientCreatedGoal;
  options: {
    goal_type: string;
  };
}

export interface ClientUpdatedGoalEvent
  extends IAventurEvent<AventurEvents.ClientUpdatedGoal> {
  event: AventurEvents.ClientUpdatedGoal;
  options: {
    goal_id: number;
    goal_type: string;
  };
}

export interface ClientDeletedGoalEvent
  extends IAventurEvent<AventurEvents.ClientDeletedGoal> {
  event: AventurEvents.ClientDeletedGoal;
  options: {
    goal_id: number;
    goal_type: string;
  };
}

export interface ClientLinkedGoalAccountsEvent
  extends IAventurEvent<AventurEvents.ClientLinkedGoalAccounts> {
  event: AventurEvents.ClientLinkedGoalAccounts;
  options: {
    goal_id: number;
    goal_type: string;
  };
}

export interface ClientUnlinkedGoalAccountsEvent
  extends IAventurEvent<AventurEvents.ClientUnlinkedGoalAccounts> {
  event: AventurEvents.ClientUnlinkedGoalAccounts;
  options: {
    goal_id: number;
    goal_type: string;
  };
}

export interface ClientCreatedAssetEvent
  extends IAventurEvent<AventurEvents.ClientCreatedAsset> {
  event: AventurEvents.ClientCreatedAsset;
  options: {
    asset_type: string;
  };
}

export interface ClientUpdatedAssetEvent
  extends IAventurEvent<AventurEvents.ClientUpdatedAsset> {
  event: AventurEvents.ClientUpdatedAsset;
  options: {
    asset_id: number;
    asset_type: string;
  };
}

export interface ClientDeletedAssetEvent
  extends IAventurEvent<AventurEvents.ClientDeletedAsset> {
  event: AventurEvents.ClientDeletedAsset;
  options: {
    asset_id: number;
    asset_type: string;
  };
}

export interface ClientCreatedDebtEvent
  extends IAventurEvent<AventurEvents.ClientCreatedDebt> {
  event: AventurEvents.ClientCreatedDebt;
  options: {
    debt_type: string;
  };
}

export interface ClientUpdatedDebtEvent
  extends IAventurEvent<AventurEvents.ClientUpdatedDebt> {
  event: AventurEvents.ClientUpdatedDebt;
  options: {
    debt_id: number;
    debt_type: string;
  };
}

export interface ClientDeletedDebtEvent
  extends IAventurEvent<AventurEvents.ClientDeletedDebt> {
  event: AventurEvents.ClientDeletedDebt;
  options: {
    debt_id: number;
    debt_type: string;
  };
}

export interface ClientCreatedIncomeEvent
  extends IAventurEvent<AventurEvents.ClientCreatedIncome> {
  event: AventurEvents.ClientCreatedIncome;
  options: {
    income_type: string;
  };
}

export interface ClientUpdatedIncomeEvent
  extends IAventurEvent<AventurEvents.ClientUpdatedIncome> {
  event: AventurEvents.ClientUpdatedIncome;
  options: {
    income_id: number;
    income_type: string;
  };
}

export interface ClientDeletedIncomeEvent
  extends IAventurEvent<AventurEvents.ClientDeletedIncome> {
  event: AventurEvents.ClientDeletedIncome;
  options: {
    income_id: number;
    income_type: string;
  };
}

export interface ClientCreatedExpenditureEvent
  extends IAventurEvent<AventurEvents.ClientCreatedExpenditure> {
  event: AventurEvents.ClientCreatedExpenditure;
  options: {
    expenditure_type: string;
  };
}

export interface ClientUpdatedExpenditureEvent
  extends IAventurEvent<AventurEvents.ClientUpdatedExpenditure> {
  event: AventurEvents.ClientUpdatedExpenditure;
  options: {
    expenditure_id: number;
    expenditure_type: string;
  };
}

export interface ClientDeletedExpenditureEvent
  extends IAventurEvent<AventurEvents.ClientDeletedExpenditure> {
  event: AventurEvents.ClientDeletedExpenditure;
  options: {
    expenditure_id: number;
    expenditure_type: string;
  };
}

export interface ClientUpdatedProfileEvent
  extends IAventurEvent<AventurEvents.ClientUpdatedProfile> {
  event: AventurEvents.ClientUpdatedProfile;
  options: {
    section_name: string;
  };
}

export interface ClientUpdatedAccountSettingsEvent
  extends IAventurEvent<AventurEvents.ClientUpdatedAccountSettings> {
  event: AventurEvents.ClientUpdatedAccountSettings;
}

type AventurEvent =
  | ClientSignedInEvent
  | ClientSignedOutEvent
  | ClientSignedUpEvent
  | ClientViewedPageEvent
  | ClientSubmittedFeedbackEvent
  | ClientClearedChatEvent
  | ClientCreatedGoalEvent
  | ClientUpdatedGoalEvent
  | ClientDeletedGoalEvent
  | ClientLinkedGoalAccountsEvent
  | ClientUnlinkedGoalAccountsEvent
  | ClientCreatedAssetEvent
  | ClientUpdatedAssetEvent
  | ClientDeletedAssetEvent
  | ClientCreatedDebtEvent
  | ClientUpdatedDebtEvent
  | ClientDeletedDebtEvent
  | ClientCreatedIncomeEvent
  | ClientUpdatedIncomeEvent
  | ClientDeletedIncomeEvent
  | ClientCreatedExpenditureEvent
  | ClientUpdatedExpenditureEvent
  | ClientDeletedExpenditureEvent
  | ClientUpdatedProfileEvent
  | ClientUpdatedAccountSettingsEvent;

type EventOptions<T extends IAventurEvent> = {
  [P1 in keyof T['options']]: P1 extends keyof T['options']
    ? T['options'][P1]
    : never;
};

// write a mapper that accepts AventurEvents enum value and returns an object of an AventurEvent type
const eventMapper = (event: AventurEvents) =>
  ({
    [AventurEvents.ClientSignedIn]: {
      event: AventurEvents.ClientSignedIn,
      display_name: 'Client Signed In',
    },
    [AventurEvents.ClientSignedOut]: {
      event: AventurEvents.ClientSignedOut,
      display_name: 'Client Signed Out',
    },
    [AventurEvents.ClientSignedUp]: {
      event: AventurEvents.ClientSignedUp,
      display_name: 'Client Signed Up',
    },
    [AventurEvents.ClientViewedPage]: {
      event: AventurEvents.ClientViewedPage,
      display_name: 'Client Viewed Page',
    },
    [AventurEvents.ClientSubmittedFeedback]: {
      event: AventurEvents.ClientSubmittedFeedback,
      display_name: 'Client Submitted Feedback',
    },
    [AventurEvents.ClientClearedChat]: {
      event: AventurEvents.ClientClearedChat,
      display_name: 'Client Cleared AI Chat',
    },
    [AventurEvents.ClientCreatedGoal]: {
      event: AventurEvents.ClientCreatedGoal,
      display_name: 'Client Created Goal',
    },
    [AventurEvents.ClientUpdatedGoal]: {
      event: AventurEvents.ClientUpdatedGoal,
      display_name: 'Client Updated Goal',
    },
    [AventurEvents.ClientDeletedGoal]: {
      event: AventurEvents.ClientDeletedGoal,
      display_name: 'Client Deleted Goal',
    },
    [AventurEvents.ClientLinkedGoalAccounts]: {
      event: AventurEvents.ClientLinkedGoalAccounts,
      display_name: 'Client Linked Goal Accounts',
    },
    [AventurEvents.ClientUnlinkedGoalAccounts]: {
      event: AventurEvents.ClientUnlinkedGoalAccounts,
      display_name: 'Client Unlinked Goal Accounts',
    },
    [AventurEvents.ClientCreatedAsset]: {
      event: AventurEvents.ClientCreatedAsset,
      display_name: 'Client Created Asset',
    },
    [AventurEvents.ClientUpdatedAsset]: {
      event: AventurEvents.ClientUpdatedAsset,
      display_name: 'Client Updated Asset',
    },
    [AventurEvents.ClientDeletedAsset]: {
      event: AventurEvents.ClientDeletedAsset,
      display_name: 'Client Deleted Asset',
    },
    [AventurEvents.ClientCreatedDebt]: {
      event: AventurEvents.ClientCreatedDebt,
      display_name: 'Client Created Debt',
    },
    [AventurEvents.ClientUpdatedDebt]: {
      event: AventurEvents.ClientUpdatedDebt,
      display_name: 'Client Updated Debt',
    },
    [AventurEvents.ClientDeletedDebt]: {
      event: AventurEvents.ClientDeletedDebt,
      display_name: 'Client Deleted Debt',
    },
    [AventurEvents.ClientCreatedIncome]: {
      event: AventurEvents.ClientCreatedIncome,
      display_name: 'Client Created Income',
    },
    [AventurEvents.ClientUpdatedIncome]: {
      event: AventurEvents.ClientUpdatedIncome,
      display_name: 'Client Updated Income',
    },
    [AventurEvents.ClientDeletedIncome]: {
      event: AventurEvents.ClientDeletedIncome,
      display_name: 'Client Deleted Income',
    },
    [AventurEvents.ClientCreatedExpenditure]: {
      event: AventurEvents.ClientCreatedExpenditure,
      display_name: 'Client Created Expenditure',
    },
    [AventurEvents.ClientUpdatedExpenditure]: {
      event: AventurEvents.ClientUpdatedExpenditure,
      display_name: 'Client Updated Expenditure',
    },
    [AventurEvents.ClientDeletedExpenditure]: {
      event: AventurEvents.ClientDeletedExpenditure,
      display_name: 'Client Deleted Expenditure',
    },
    [AventurEvents.ClientUpdatedProfile]: {
      event: AventurEvents.ClientUpdatedProfile,
      display_name: 'Client Updated Profile',
    },
    [AventurEvents.ClientUpdatedAccountSettings]: {
      event: AventurEvents.ClientUpdatedAccountSettings,
      display_name: 'Client Updated Account Settings',
    },
  })[event] as IAventurEvent;

const trackAventurEvent = <T extends AventurEvent>(
  event: T['event'],
  options?: EventOptions<T>,
) => {
  if (import.meta.env.VITE_AMPLITUDE_KEY) {
    const event_type = AMPLITUDE_EVENT_TYPE;
    const { display_name } = eventMapper(event);

    track(display_name, {
      event_type,
      ...(options ?? {}),
    });
  }
};

export const useEventTracking = (router?: Router) => {
  router?.beforeEach((to, from, next) => {
    // skip tracking initial sign-in navigation (dashboard > dashboard)
    // and logout action navigation (<> logout)
    if (
      to.path !== from.path &&
      to.name !== 'logout' &&
      from.name !== 'logout'
    ) {
      console.log(to.meta);
      to.meta.view_started = Date.now();
      trackAventurEvent<ClientViewedPageEvent>(AventurEvents.ClientViewedPage, {
        page_name: to.meta?.title as string | undefined,
        params: to.params,
      });
    }

    next();
  });

  return {
    setTrackingIdentity: setAmplitudeUserDetails,
    trackEvent: trackAventurEvent,
  };
};
