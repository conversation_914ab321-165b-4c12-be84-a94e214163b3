import { App } from 'vue';
import * as amplitude from '@amplitude/analytics-browser';

import { User } from '@aventur-shared/modules/users';
//

export default {
  install: async (app: App) => {
    if (import.meta.env.VITE_AMPLITUDE_KEY) {
      amplitude.init(String(import.meta.env.VITE_AMPLITUDE_KEY), {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        appVersion: __APP_VERSION__,
        autocapture: {
          attribution: false,
          pageViews: true,
          sessions: false,
          formInteractions: false,
          fileDownloads: true,
          elementInteractions: false,
        },
        serverZone: 'EU',
      });
    }
  },
};

export function setAmplitudeUserDetails(user: User) {
  if (import.meta.env.VITE_AMPLITUDE_KEY) {
    amplitude.setUserId(user.email);
    const identify = new amplitude.Identify();
    identify
      .set('jarvis_id', user.id)
      .set('first_name', user.firstName)
      .set('last_name', user.lastName);
    amplitude.identify(identify);
    amplitude.setGroup('user-role', user.groups ?? '');
  }
}

export const track = amplitude.track;
