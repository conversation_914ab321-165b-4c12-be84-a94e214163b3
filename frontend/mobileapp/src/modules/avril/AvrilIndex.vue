<template>
  <div
    v-if="loadedMessages && !chatbotStore.isBusy"
    class="flex h-full flex-col"
  >
    <x-mark-icon
      class="text-primary fixed left-5 top-14 size-6 cursor-pointer"
      @click="handleClose"
    />
    <div
      class="fixed right-6 top-4 z-50 flex flex-col gap-1 rounded-lg bg-white p-1 md:flex-row"
    >
      <button
        class="w-28 cursor-pointer border-none bg-gray-400 p-1 text-white shadow-md"
        @click="handleClearChat"
      >
        Clear Chat
      </button>
    </div>
    <div class="flex h-full flex-col">
      <div v-if="activeTab === Tab.Chat">
        <div
          class="flex max-h-[94vh] flex-1 flex-col overflow-y-auto"
          ref="chatContainer"
        >
          <div class="flex flex-1 flex-col px-5 pt-5">
            <div
              class="animate-gradient mb-10 mt-[30vh] flex size-16 items-center justify-center rounded-full bg-gradient-to-r from-purple-300 to-lime-100 bg-[length:200%_200%] p-[2px]"
            ></div>

            <div
              v-for="(message, index) in messages"
              :key="index"
              class="flex flex-1 flex-col"
            >
              <component
                :is="chatMessageLayoutMap[message.messageLayout]"
                :message="message"
              />
              <div
                v-if="
                  message.messageLayout === 'user_text_layout' ||
                  message.messageLayout === 'assistant_text_layout'
                "
                class="mb-4 text-xs"
                :class="{
                  'text-right': message.messageLayout === 'user_text_layout',
                  'text-left': message.messageLayout !== 'user_text_layout',
                }"
              >
                {{ formatToChatFeedTimestamp(message) }}
              </div>
            </div>
          </div>
          <div
            class="mx-5 my-2 mb-9 flex max-w-[calc(100%-40px)] items-center self-start px-2 py-1 text-sm italic text-gray-400 opacity-0 transition-[opacity_0.3s,visibility_0.3s]"
            :class="{ 'visible opacity-80': isTyping }"
          >
            <div class="mr-2 flex w-12 items-center justify-center space-x-2">
              <div
                class="mt-0.5 size-2 animate-bounce rounded-full bg-purple-500 [animation-delay:-0.3s]"
              ></div>
              <div
                class="mt-0.5 size-2 animate-bounce rounded-full bg-purple-500 [animation-delay:-0.15s]"
              ></div>
              <div
                class="mt-0.5 size-2 animate-bounce rounded-full bg-purple-500"
              ></div>
            </div>
            Avril is typing
          </div>
        </div>

        <div
          class="fixed inset-x-0 bottom-0 z-50 flex bg-gray-100 px-2 pb-2"
          :class="{
            'pointer-events-none opacity-30': isTyping,
          }"
        >
          <div
            class="animate-gradient flex flex-1 rounded-full bg-gradient-to-r from-lime-100 to-purple-300 bg-[length:200%_200%] p-[2px]"
          >
            <form @submit.prevent="submit" class="flex flex-1">
              <text-field
                v-show="false"
                label="Message Layout"
                name="messageLayout"
              />
              <input
                ref="inputRef"
                v-model="newMessage"
                class="flex-1 grow rounded-full border-none p-3 text-gray-800 caret-gray-800 focus:outline-none focus:ring-0 disabled:cursor-not-allowed disabled:bg-gray-100 disabled:opacity-50"
                placeholder="Start Typing"
                :disabled="isTyping || isDisabled"
                @focus="wasFocused = true"
                @blur="wasFocused = false"
                @keyup.enter="submit"
              />
            </form>
          </div>
        </div>
      </div>

      <div
        v-else-if="activeTab === Tab.Analytics"
        class="flex flex-1 flex-col overflow-y-auto px-4 pb-0 pt-5"
      >
        <div v-for="(message, index) in messages" :key="index" class="mb-2">
          <component
            :is="analyticsMessageLayoutMap[message.messageLayout ?? '']"
            :message="message"
            :disabled="true"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';
  import { useForm } from 'vee-validate';
  import { useRouter } from 'vue-router';
  import { XMarkIcon } from '@heroicons/vue/24/solid';
  import { computed, nextTick, onMounted, ref, watch } from 'vue';

  import { TextField } from '@aventur-shared/components/form';
  import UserText from '@aventur-shared/modules/chatbot/components/messages/UserText.vue';
  import AssistantText from '@aventur-shared/modules/chatbot/components/messages/AssistantText.vue';
  import ConsentForm from '@aventur-shared/modules/chatbot/components/messages/ConsentForm.vue';
  import WithdrawConsentForm from '@aventur-shared/modules/chatbot/components/messages/WithdrawConsentForm.vue';
  import Company from '@aventur-shared/modules/chatbot/components/messages/data/Company.vue';
  import UserHealthScore from '@aventur-shared/modules/chatbot/components/messages/data/UserHealthScore.vue';
  import UserPersonalDetails from '@aventur-shared/modules/chatbot/components/messages/data/UserPersonalDetails.vue';
  import UserRelations from '@aventur-shared/modules/chatbot/components/messages/data/UserRelations.vue';
  import UserAddresses from '@aventur-shared/modules/chatbot/components/messages/data/UserAddresses.vue';
  import {
    formatToChatFeedTimestamp,
    scrollToBottom,
  } from '@aventur-shared/modules/chatbot/utils/helpers';
  import { useChatbotStore } from '@aventur-shared/modules/chatbot/stores/chatbotStore';
  import { Tab } from '@aventur-shared/modules/chatbot/types/chat-tab';
  import {
    AllFormValues,
    UserTextFormValues,
    getInitialValues,
    isEditableDataForm as hasCompletedProperty,
    isConsentForm,
    isWithdrawConsentForm,
  } from '@aventur-shared/modules/chatbot/types/form-model';
  import { validationSchema } from '@aventur-shared/modules/chatbot/services/validation/form-validation';

  import {
    AventurEvents,
    type ClientClearedChatEvent,
    useEventTracking,
  } from '@/composables/useEventTracking';

  const chatContainer = ref<HTMLElement | null>(null);

  const router = useRouter();
  const { trackEvent } = useEventTracking();

  const newMessage = ref<string>('');

  const inputRef = ref<HTMLInputElement | null>(null);
  const wasFocused = ref(false);

  const chatbotStore = useChatbotStore();
  const { messages, activeTab, isTyping } = storeToRefs(chatbotStore);
  const loadedMessages = ref<boolean>(false);

  const { handleSubmit } = useForm<AllFormValues>({
    validationSchema: validationSchema['user_text_layout'],
    initialValues: getInitialValues('user_text_layout'),
  });

  const submit = handleSubmit(async (fv: AllFormValues) => {
    const msg = newMessage.value.trim();
    if (msg) {
      newMessage.value = '';

      const message: UserTextFormValues = {
        messageLayout: 'user_text_layout',
        message: msg,
      };

      chatbotStore.postMessage(message, true);
    }
  });

  const isDisabled = computed(() => {
    let disabled = false;
    if (messages.value !== undefined) {
      disabled = messages.value.some((msg) => {
        // Check for pending permission forms
        if (isConsentForm(msg)) {
          return true;
        }
        // Check for withdraw consent forms - disable if pending or if withdrawal was confirmed
        if (isWithdrawConsentForm(msg)) {
          // If not completed, it's still pending
          if (!msg.completed) {
            return true;
          }
          // If completed and withdrawal was confirmed, keep chatbox disabled
          if (msg.withdrawn === true) {
            return true;
          }
        }
        return false;
      });
    }

    return disabled;
  });

  const hasWithdrawnConsent = computed(() => {
    if (messages.value && messages.value.length > 0) {
      // Look for the most recent withdrawal form (not just the last message)
      for (let i = messages.value.length - 1; i >= 0; i--) {
        const message = messages.value[i];
        if (isWithdrawConsentForm(message)) {
          return message.completed === true && message.withdrawn === true;
        }
      }
    }
    return false;
  });

  const handleClose = () => {
    if (hasWithdrawnConsent.value) {
      // If consent was withdrawn, clear the conversation and go back
      chatbotStore.newConversation();
      router.go(-1);
    } else {
      // Otherwise, just go back
      router.go(-1);
    }
  };

  const handleClearChat = () => {
    chatbotStore.newConversation();
    trackEvent<ClientClearedChatEvent>(AventurEvents.ClientClearedChat);
  };

  const chatMessageLayoutMap = {
    user_text_layout: UserText,
    assistant_text_layout: AssistantText,
    consent_form_layout: ConsentForm,
    user__consent_withdraw_layout: WithdrawConsentForm,
    company_layout: Company,
    user__health_score_layout: UserHealthScore,
    user__personal_details_layout: UserPersonalDetails,
    user__addresses_layout: UserAddresses,
    user__relations_layout: UserRelations,
  };

  const analyticsMessageLayoutMap = {};

  let isMounted = false;

  // Watch for tab switch and scroll to bottom
  watch(activeTab, async () => {
    if (isMounted) {
      await nextTick();
      scrollToBottom(chatContainer);
    }
  });

  watch(
    messages,
    async () => {
      await nextTick();
      scrollToBottom(chatContainer);
    },
    { deep: true },
  );

  watch(isTyping, async (newVal, oldVal) => {
    if (oldVal === true && newVal === false && wasFocused) {
      await nextTick();
      inputRef.value?.focus();
    }
  });

  // Initialize by fetching existing messages
  onMounted(async () => {
    messages.value = await chatbotStore.fetchMessages({ force: true });

    await nextTick();
    if (isMounted) {
      scrollToBottom(chatContainer);
    }

    loadedMessages.value = true;
    isMounted = true;
  });
</script>
