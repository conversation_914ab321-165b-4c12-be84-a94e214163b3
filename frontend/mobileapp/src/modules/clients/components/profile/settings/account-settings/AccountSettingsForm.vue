<template>
  <FormWrapper>
    <AccountSettingsForm @on-cancel="back" @on-update="onUpdate" />
  </FormWrapper>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';

  import { useUserStore } from '@aventur-shared/modules/users';
  import { useClientStore } from '@aventur-shared/modules/clients';
  import { useToast } from '@aventur-shared/composables/useToast';

  import {
    AventurEvents,
    type ClientUpdatedAccountSettingsEvent,
    useEventTracking,
  } from '@/composables/useEventTracking';
  import FormWrapper from '@modules/clients/components/profile/personal-details/FormWrapper.vue';
  import { patchClientAccount } from '@modules/clients/api/patch-client-account';
  import AccountSettingsForm from './form/_form.vue';
  //

  const router = useRouter();
  const toast = useToast();
  const { trackEvent } = useEventTracking();

  const { signOut } = useUserStore();
  const { resetClient } = useClientStore();

  const back = () => router.go(-1);

  const onUpdate = async ({ clientId, email }) => {
    try {
      await patchClientAccount(clientId, {
        email,
      });
      toast.success(`Please, check your email for new sign-in details.`);

      trackEvent<ClientUpdatedAccountSettingsEvent>(
        AventurEvents.ClientUpdatedAccountSettings,
      );

      resetClient();
      await signOut().then(() => router.push('/'));
    } catch (e) {
      toast.error(e as Error);
    }
  };
</script>
