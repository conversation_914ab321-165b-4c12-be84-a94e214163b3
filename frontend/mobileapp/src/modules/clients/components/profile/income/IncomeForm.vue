<template>
  <FormWrapper>
    <IncomeForm
      :income="income"
      @on-add="onAdd"
      @on-update="onUpdate"
      @on-delete="onDelete"
      @on-cancel="back"
    />
  </FormWrapper>
</template>

<script setup lang="ts">
  import { onBeforeMount, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  import { useListCrud } from '@aventur-shared/utils/list';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { useConfirmation } from '@aventur-shared/composables/useConfirmation';
  import { useRefData } from '@aventur-shared/stores';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { ClientId } from '@aventur-shared/modules/clients';
  import {
    Income,
    IncomeListItem,
    postIncomes,
    useFactfindStore,
  } from '@aventur-shared/modules/factfind';

  import {
    AventurEvents,
    type ClientCreatedIncomeEvent,
    type ClientDeletedIncomeEvent,
    type ClientUpdatedIncomeEvent,
    useEventTracking,
  } from '@/composables/useEventTracking';
  import FormWrapper from './FormWrapper.vue';
  import IncomeForm from './form/form.vue';
  //

  const route = useRoute();
  const router = useRouter();
  const toast = useToast();
  const { trackEvent } = useEventTracking();

  const { getCashflowNameFromId } = useRefData();
  const { userId } = useUserStore();
  const { getIncomes } = useFactfindStore();

  const income = ref<IncomeListItem>();
  const back = () => router.go(-1);

  const {
    list: incomeList,
    crud: incomeCrud,
    reset: resetIncomeList,
    isModified: _isIncomeListModified,
  } = useListCrud<Income>(getIncomes);

  onBeforeMount(() => {
    income.value = incomeList.value.find(
      (item) => item.id === Number(route.params.id),
    );
  });

  const action = async (items: Income[]) => {
    try {
      await postIncomes(userId as ClientId, items);
      toast.success(`Your details have been updated`);
      back();
    } catch (e) {
      toast.error(e as Error);
      resetIncomeList();
    }
  };

  const onAdd = async (item: Income) => {
    incomeCrud.add(item);
    await action(incomeList.value);

    trackEvent<ClientCreatedIncomeEvent>(AventurEvents.ClientCreatedIncome, {
      income_type: getCashflowNameFromId(item.type) || 'Unknown',
    });
  };

  const onUpdate = async (item: IncomeListItem) => {
    incomeCrud.edit(item);
    await action(incomeList.value);

    trackEvent<ClientUpdatedIncomeEvent>(AventurEvents.ClientUpdatedIncome, {
      income_id: item.id as number,
      income_type: getCashflowNameFromId(item.type) || 'Unknown',
    });
  };

  const onDelete = async (item: IncomeListItem) => {
    const { isAccepted } = await useConfirmation(
      `Are you sure you want to delete this item?`,
    );
    if (isAccepted()) {
      incomeCrud.remove(item.key);
      await action(incomeList.value);

      trackEvent<ClientDeletedIncomeEvent>(AventurEvents.ClientDeletedIncome, {
        income_id: item.id as number,
        income_type: getCashflowNameFromId(item.type) || 'Unknown',
      });
    }
  };
</script>
