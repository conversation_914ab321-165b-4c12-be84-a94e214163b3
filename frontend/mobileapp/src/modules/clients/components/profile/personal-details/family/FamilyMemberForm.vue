<template>
  <FormWrapper>
    <FamilyMemberForm
      :member="member"
      :type="member ? 'EDIT' : 'ADD'"
      @on-cancel="back"
      @on-add="onAdd"
      @on-update="onUpdate"
      @on-delete="onDelete"
    />
  </FormWrapper>
</template>

<script setup lang="ts">
  import { onBeforeMount, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { Dialog } from '@capacitor/dialog';

  import { useToast } from '@aventur-shared/composables/useToast';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { useListCrud } from '@aventur-shared/utils/list';
  import {
    postClientAboutYou,
    useFactfindStore,
  } from '@aventur-shared/modules/factfind';

  import {
    AventurEvents,
    ClientUpdatedProfileEvent,
    useEventTracking,
  } from '@/composables/useEventTracking';
  import FormWrapper from '../FormWrapper.vue';
  import FamilyMemberForm from './form/_form.vue';
  import { FamilyMember, FamilyMembersListItem } from './form/form-model';
  //

  const route = useRoute();
  const router = useRouter();
  const toast = useToast();
  const { trackEvent } = useEventTracking();

  const { userId } = useUserStore();
  const {
    primaryDetails,
    familyMembers,
    getClientPrimaryDetails,
    setClientPrimaryDetails,
  } = useFactfindStore();

  const member = ref<FamilyMembersListItem>();
  const back = () => router.push('/profile/family');

  onBeforeMount(() => {
    member.value = list.value.find(
      (item) => item.id === Number(route.params.id),
    );
  });

  const { list, crud, reset } = useListCrud<FamilyMember>(familyMembers);

  const action = async (items: FamilyMember[]) => {
    try {
      const {
        personalDetails,
        contactDetails,
        furtherInformations,
        retirementDetails,
      } = primaryDetails;

      await postClientAboutYou(userId as ClientId, {
        personalDetails,
        contactDetails,
        furtherInformations,
        retirementDetails,
        familyMembers: items,
      });
      toast.success(`Your details have been updated`);

      setClientPrimaryDetails(
        await getClientPrimaryDetails(userId as ClientId),
      );

      trackEvent<ClientUpdatedProfileEvent>(
        AventurEvents.ClientUpdatedProfile,
        {
          section_name: 'family',
        },
      );

      back().then();
    } catch (e) {
      reset();
      toast.error(e as Error);
    }
  };

  const onAdd = async (item: FamilyMember) => {
    crud.add(item);
    await action(list.value);
  };

  const onUpdate = async (item: FamilyMembersListItem) => {
    crud.edit(item);
    await action(list.value);
  };

  const onDelete = async (item: FamilyMembersListItem) => {
    const { value } = await Dialog.confirm({
      message: `Are you sure you want to delete this item?`,
    });

    if (value) {
      crud.remove(item.key);
      await action(list.value);
    }
  };
</script>
