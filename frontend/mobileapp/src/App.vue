<template>
  <AppWrapper>
    <template #default="{ showIntro, authInitialState }">
      <Splash v-if="isReady === false && user === undefined">
        <MaterialDesignIcon icon="lock" class="text-2xl text-white" />
        <LoadingSpinner class="absolute size-20 text-[#C9E386]" />
      </Splash>
      <Auth
        v-else-if="showIntro === false || user"
        :initialState="authInitialState"
      />
    </template>
  </AppWrapper>
</template>

<script setup lang="ts">
  import { onBeforeMount, ref } from 'vue';
  import { useTimeout, useTimeoutFn } from '@vueuse/core';
  import { SplashScreen } from '@capacitor/splash-screen';
  import {
    GetCurrentUserOutput,
    getCurrentUser,
    signOut,
  } from '@aws-amplify/auth';

  import LoadingSpinner from '@aventur-shared/components/LoadingSpinner.vue';
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';

  import AppWrapper from '@/components/AppWrapper.vue';
  import Splash from '@/components/Splash.vue';
  import Auth from '@/components/auth/Auth.vue';
  //

  const { ready: isReady, start } = useTimeout(1500, { controls: true });
  const user = ref<GetCurrentUserOutput>();

  useTimeoutFn(async () => {
    await SplashScreen.hide();
    start();
  }, 1000);

  onBeforeMount(async () => {
    // ensure user is signed out on a fresh app start
    await signOut();
    try {
      user.value = await getCurrentUser();
    } catch {
      //
    }
  });
</script>

<style lang="postcss">
  @import 'material-icons/iconfont/material-icons.css';
</style>
